# Jenkins Pipeline 学习指南

## 目录结构
```
Jenkins pipeline/
├── README.md                    # 学习指南
├── examples/                    # 示例文件
│   ├── basic/                   # 基础示例
│   ├── advanced/                # 高级示例
│   └── real-world/              # 实际项目示例
├── docs/                        # 学习文档
└── practice/                    # 练习项目
```

## 学习路径

### 第一阶段：基础概念
1. **Pipeline基本概念**
   - 什么是Pipeline
   - 声明式 vs 脚本式
   - Jenkinsfile的作用

2. **核心组件**
   - Agent（代理）
   - Stages（阶段）
   - Steps（步骤）
   - Post（后处理）

### 第二阶段：声明式Pipeline
1. **基本语法**
   - pipeline块
   - agent指令
   - stages和stage
   - steps

2. **常用指令**
   - when条件
   - parallel并行
   - environment环境变量
   - parameters参数

### 第三阶段：高级特性
1. **流程控制**
   - 条件执行
   - 并行执行
   - 错误处理

2. **集成功能**
   - 代码检出
   - 测试集成
   - 构建和部署
   - 通知机制

## 快速开始

1. 查看 `examples/basic/` 目录下的基础示例
2. 阅读 `docs/` 目录下的详细文档
3. 在 `practice/` 目录下进行实际练习

## 学习资源

- [Jenkins官方文档](https://www.jenkins.io/doc/book/pipeline/)
- [Pipeline语法参考](https://www.jenkins.io/doc/book/pipeline/syntax/)
- [最佳实践指南](https://www.jenkins.io/doc/book/pipeline/pipeline-best-practices/)
