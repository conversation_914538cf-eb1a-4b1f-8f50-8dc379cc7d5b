# 声明式Pipeline语法详解

## 1. 基本结构

声明式Pipeline必须包含在`pipeline`块中：

```groovy
pipeline {
    // Pipeline配置
}
```

## 2. 核心指令

### agent
指定Pipeline或特定stage的执行环境。

```groovy
pipeline {
    agent any                    // 任何可用代理
    // agent none                // 不分配全局代理
    // agent { label 'linux' }   // 指定标签的代理
    // agent { docker 'node:14' } // Docker容器
}
```

### stages和stage
定义Pipeline的执行阶段。

```groovy
stages {
    stage('构建') {
        steps {
            echo '执行构建'
        }
    }
    stage('测试') {
        steps {
            echo '执行测试'
        }
    }
}
```

### steps
包含在stage中的具体执行步骤。

```groovy
steps {
    echo 'Hello World'
    sh 'make build'
    bat 'build.bat'
    script {
        // Groovy脚本
        def version = readFile('version.txt')
        echo "Version: ${version}"
    }
}
```

## 3. 可选指令

### environment
定义环境变量。

```groovy
environment {
    // 全局环境变量
    CC = 'clang'
    PATH = "/usr/local/bin:${env.PATH}"
}

stage('构建') {
    environment {
        // 阶段级环境变量
        DEBUG = 'true'
    }
    steps {
        echo "编译器: ${CC}"
        echo "调试模式: ${DEBUG}"
    }
}
```

### parameters
定义构建参数。

```groovy
parameters {
    string(name: 'VERSION', defaultValue: '1.0', description: '版本号')
    choice(name: 'ENVIRONMENT', choices: ['dev', 'test', 'prod'], description: '环境')
    booleanParam(name: 'SKIP_TESTS', defaultValue: false, description: '跳过测试')
    text(name: 'NOTES', defaultValue: '', description: '发布说明')
    password(name: 'API_KEY', defaultValue: '', description: 'API密钥')
}
```

### triggers
定义自动触发条件。

```groovy
triggers {
    // 定时触发 (cron语法)
    cron('H 2 * * *')           // 每天凌晨2点
    
    // 轮询SCM变化
    pollSCM('H/5 * * * *')      // 每5分钟检查一次
    
    // 上游项目触发
    upstream(upstreamProjects: 'upstream-job', threshold: hudson.model.Result.SUCCESS)
}
```

### options
Pipeline级别的选项。

```groovy
options {
    // 构建保留策略
    buildDiscarder(logRotator(numToKeepStr: '10'))
    
    // 超时设置
    timeout(time: 1, unit: 'HOURS')
    
    // 禁用并发构建
    disableConcurrentBuilds()
    
    // 跳过默认的代码检出
    skipDefaultCheckout()
    
    // 时间戳
    timestamps()
    
    // 重试次数
    retry(3)
}
```

### tools
自动安装和配置工具。

```groovy
tools {
    maven 'Maven-3.8.1'
    jdk 'JDK-11'
    nodejs 'NodeJS-14'
}
```

## 4. 条件执行 (when)

```groovy
stage('部署') {
    when {
        // 分支条件
        branch 'main'
        
        // 环境变量条件
        environment name: 'DEPLOY', value: 'true'
        
        // 表达式条件
        expression { return params.ENVIRONMENT == 'prod' }
        
        // 组合条件
        allOf {
            branch 'main'
            environment name: 'DEPLOY', value: 'true'
        }
        
        anyOf {
            branch 'main'
            branch 'develop'
        }
        
        not {
            branch 'feature/*'
        }
    }
    steps {
        echo '执行部署'
    }
}
```

## 5. 并行执行

```groovy
stage('并行测试') {
    parallel {
        stage('单元测试') {
            steps {
                sh 'npm test'
            }
        }
        stage('集成测试') {
            steps {
                sh 'npm run integration-test'
            }
        }
        stage('代码质量检查') {
            steps {
                sh 'npm run lint'
            }
        }
    }
}
```

## 6. 后处理 (post)

```groovy
post {
    always {
        echo '总是执行'
        // 清理工作空间
        cleanWs()
    }
    
    success {
        echo '成功时执行'
        // 发送成功通知
        mail to: '<EMAIL>',
             subject: "构建成功: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
             body: "构建成功完成"
    }
    
    failure {
        echo '失败时执行'
        // 发送失败通知
    }
    
    unstable {
        echo '不稳定时执行'
    }
    
    changed {
        echo '状态改变时执行'
    }
    
    fixed {
        echo '从失败恢复时执行'
    }
    
    regression {
        echo '从成功变为失败时执行'
    }
    
    cleanup {
        echo '最后执行的清理工作'
    }
}
```

## 7. 输入和批准

```groovy
stage('部署确认') {
    steps {
        script {
            // 简单确认
            input message: '确认部署到生产环境?', ok: '部署'
            
            // 带参数的输入
            def userInput = input(
                message: '选择部署选项',
                parameters: [
                    choice(name: 'ENVIRONMENT', choices: ['staging', 'production']),
                    booleanParam(name: 'ROLLBACK', defaultValue: false, description: '是否支持回滚')
                ]
            )
            
            echo "选择的环境: ${userInput.ENVIRONMENT}"
            echo "支持回滚: ${userInput.ROLLBACK}"
        }
    }
}
```

## 8. 错误处理

```groovy
stage('构建') {
    steps {
        script {
            try {
                sh 'make build'
            } catch (Exception e) {
                echo "构建失败: ${e.getMessage()}"
                currentBuild.result = 'FAILURE'
                error("构建过程中发生错误")
            }
        }
    }
}
```

## 9. 常用步骤

```groovy
steps {
    // 文件操作
    writeFile file: 'config.txt', text: 'configuration data'
    def content = readFile('config.txt')
    
    // 归档构件
    archiveArtifacts artifacts: 'dist/**', fingerprint: true
    
    // 发布测试结果
    publishTestResults testResultsPattern: 'test-results.xml'
    
    // 发布HTML报告
    publishHTML([
        allowMissing: false,
        alwaysLinkToLastBuild: true,
        keepAll: true,
        reportDir: 'coverage',
        reportFiles: 'index.html',
        reportName: 'Coverage Report'
    ])
    
    // 设置构建描述
    currentBuild.description = "Version ${params.VERSION}"
    
    // 设置构建显示名称
    currentBuild.displayName = "#${BUILD_NUMBER}-${params.VERSION}"
}
```

## 10. 最佳实践

1. **使用有意义的stage名称**
2. **合理使用并行执行**
3. **适当的错误处理**
4. **清理工作空间**
5. **使用环境变量管理配置**
6. **添加适当的通知机制**

## 下一步

学习完声明式Pipeline语法后，建议：
1. 实践基本的Pipeline
2. 学习高级特性
3. 集成实际的构建工具
4. 添加测试和部署流程
