// 高级并行和矩阵构建示例
// 演示如何在多个环境和配置下并行构建和测试

pipeline {
    agent none
    
    parameters {
        choice(
            name: 'BUILD_MATRIX',
            choices: ['minimal', 'full', 'custom'],
            description: '选择构建矩阵'
        )
        
        string(
            name: 'CUSTOM_PLATFORMS',
            defaultValue: 'linux,windows',
            description: '自定义平台列表（逗号分隔）'
        )
    }
    
    environment {
        DOCKER_REGISTRY = 'your-registry.com'
        APP_NAME = 'multi-platform-app'
    }
    
    stages {
        stage('准备构建矩阵') {
            agent any
            steps {
                script {
                    // 定义构建矩阵
                    def buildMatrix = [:]
                    
                    switch(params.BUILD_MATRIX) {
                        case 'minimal':
                            buildMatrix = [
                                'linux-node14': [platform: 'linux', runtime: 'node:14'],
                                'windows-node14': [platform: 'windows', runtime: 'node:14']
                            ]
                            break
                            
                        case 'full':
                            buildMatrix = [
                                'linux-node14': [platform: 'linux', runtime: 'node:14'],
                                'linux-node16': [platform: 'linux', runtime: 'node:16'],
                                'linux-node18': [platform: 'linux', runtime: 'node:18'],
                                'windows-node14': [platform: 'windows', runtime: 'node:14'],
                                'windows-node16': [platform: 'windows', runtime: 'node:16'],
                                'macos-node16': [platform: 'macos', runtime: 'node:16']
                            ]
                            break
                            
                        case 'custom':
                            def platforms = params.CUSTOM_PLATFORMS.split(',')
                            platforms.each { platform ->
                                buildMatrix["${platform.trim()}-node16"] = [
                                    platform: platform.trim(),
                                    runtime: 'node:16'
                                ]
                            }
                            break
                    }
                    
                    // 存储矩阵配置
                    env.BUILD_MATRIX_JSON = writeJSON returnText: true, json: buildMatrix
                    echo "构建矩阵: ${env.BUILD_MATRIX_JSON}"
                }
            }
        }
        
        stage('并行构建和测试') {
            steps {
                script {
                    def buildMatrix = readJSON text: env.BUILD_MATRIX_JSON
                    def parallelBuilds = [:]
                    
                    buildMatrix.each { buildName, config ->
                        parallelBuilds[buildName] = {
                            node(config.platform) {
                                stage("${buildName} - 检出代码") {
                                    checkout scm
                                    echo "在 ${config.platform} 平台检出代码"
                                }
                                
                                stage("${buildName} - 环境准备") {
                                    script {
                                        if (config.platform == 'linux') {
                                            sh """
                                                echo "准备 Linux 环境"
                                                echo "运行时: ${config.runtime}"
                                                docker --version || echo "Docker 未安装"
                                            """
                                        } else if (config.platform == 'windows') {
                                            bat """
                                                echo "准备 Windows 环境"
                                                echo "运行时: ${config.runtime}"
                                                docker --version || echo "Docker 未安装"
                                            """
                                        } else {
                                            echo "准备 ${config.platform} 环境"
                                        }
                                    }
                                }
                                
                                stage("${buildName} - 依赖安装") {
                                    script {
                                        if (config.platform == 'linux') {
                                            sh """
                                                echo "安装依赖 - Linux"
                                                # npm install
                                                echo "依赖安装完成"
                                            """
                                        } else if (config.platform == 'windows') {
                                            bat """
                                                echo "安装依赖 - Windows"
                                                REM npm install
                                                echo "依赖安装完成"
                                            """
                                        }
                                    }
                                }
                                
                                stage("${buildName} - 构建") {
                                    script {
                                        try {
                                            if (config.platform == 'linux') {
                                                sh """
                                                    echo "构建应用 - ${config.runtime}"
                                                    mkdir -p dist/${buildName}
                                                    echo "Build for ${buildName}" > dist/${buildName}/build.txt
                                                    echo "Platform: ${config.platform}" >> dist/${buildName}/build.txt
                                                    echo "Runtime: ${config.runtime}" >> dist/${buildName}/build.txt
                                                """
                                            } else if (config.platform == 'windows') {
                                                bat """
                                                    echo "构建应用 - ${config.runtime}"
                                                    if not exist dist\\${buildName} mkdir dist\\${buildName}
                                                    echo Build for ${buildName} > dist\\${buildName}\\build.txt
                                                    echo Platform: ${config.platform} >> dist\\${buildName}\\build.txt
                                                    echo Runtime: ${config.runtime} >> dist\\${buildName}\\build.txt
                                                """
                                            }
                                            
                                            // 归档构建产物
                                            archiveArtifacts artifacts: "dist/${buildName}/**", allowEmptyArchive: true
                                            
                                        } catch (Exception e) {
                                            echo "构建失败: ${e.getMessage()}"
                                            currentBuild.result = 'UNSTABLE'
                                        }
                                    }
                                }
                                
                                stage("${buildName} - 测试") {
                                    parallel {
                                        "单元测试": {
                                            script {
                                                try {
                                                    if (config.platform == 'linux') {
                                                        sh """
                                                            echo "运行单元测试 - ${buildName}"
                                                            # npm test
                                                            echo "单元测试通过"
                                                        """
                                                    } else if (config.platform == 'windows') {
                                                        bat """
                                                            echo "运行单元测试 - ${buildName}"
                                                            REM npm test
                                                            echo "单元测试通过"
                                                        """
                                                    }
                                                } catch (Exception e) {
                                                    echo "单元测试失败: ${e.getMessage()}"
                                                    currentBuild.result = 'UNSTABLE'
                                                }
                                            }
                                        }
                                        
                                        "集成测试": {
                                            script {
                                                try {
                                                    if (config.platform == 'linux') {
                                                        sh """
                                                            echo "运行集成测试 - ${buildName}"
                                                            # npm run integration-test
                                                            echo "集成测试通过"
                                                        """
                                                    } else if (config.platform == 'windows') {
                                                        bat """
                                                            echo "运行集成测试 - ${buildName}"
                                                            REM npm run integration-test
                                                            echo "集成测试通过"
                                                        """
                                                    }
                                                } catch (Exception e) {
                                                    echo "集成测试失败: ${e.getMessage()}"
                                                    currentBuild.result = 'UNSTABLE'
                                                }
                                            }
                                        }
                                        
                                        "性能测试": {
                                            script {
                                                if (config.platform == 'linux') {
                                                    sh """
                                                        echo "运行性能测试 - ${buildName}"
                                                        # npm run performance-test
                                                        echo "性能测试完成"
                                                    """
                                                } else if (config.platform == 'windows') {
                                                    bat """
                                                        echo "运行性能测试 - ${buildName}"
                                                        REM npm run performance-test
                                                        echo "性能测试完成"
                                                    """
                                                }
                                            }
                                        }
                                    }
                                }
                                
                                stage("${buildName} - 打包") {
                                    script {
                                        if (config.platform == 'linux') {
                                            sh """
                                                echo "打包应用 - ${buildName}"
                                                tar -czf ${buildName}-package.tar.gz dist/${buildName}/
                                                echo "打包完成"
                                            """
                                        } else if (config.platform == 'windows') {
                                            bat """
                                                echo "打包应用 - ${buildName}"
                                                powershell Compress-Archive -Path dist\\${buildName}\\* -DestinationPath ${buildName}-package.zip
                                                echo "打包完成"
                                            """
                                        }
                                        
                                        // 归档打包文件
                                        archiveArtifacts artifacts: "${buildName}-package.*", allowEmptyArchive: true
                                    }
                                }
                            }
                        }
                    }
                    
                    // 执行并行构建
                    parallel parallelBuilds
                }
            }
        }
        
        stage('构建结果汇总') {
            agent any
            steps {
                script {
                    echo "=== 构建结果汇总 ==="
                    echo "构建矩阵: ${params.BUILD_MATRIX}"
                    echo "构建状态: ${currentBuild.result ?: 'SUCCESS'}"
                    
                    def buildMatrix = readJSON text: env.BUILD_MATRIX_JSON
                    buildMatrix.each { buildName, config ->
                        echo "✓ ${buildName}: ${config.platform} - ${config.runtime}"
                    }
                    
                    // 生成构建报告
                    writeFile file: 'build-report.txt', text: """
构建报告
========
构建时间: ${new Date()}
构建号: ${env.BUILD_NUMBER}
构建矩阵: ${params.BUILD_MATRIX}
构建状态: ${currentBuild.result ?: 'SUCCESS'}

构建详情:
${buildMatrix.collect { k, v -> "- ${k}: ${v.platform} - ${v.runtime}" }.join('\n')}
"""
                    
                    archiveArtifacts artifacts: 'build-report.txt', allowEmptyArchive: true
                }
            }
        }
        
        stage('部署决策') {
            agent any
            when {
                expression { currentBuild.result == null || currentBuild.result == 'SUCCESS' }
            }
            steps {
                script {
                    def shouldDeploy = input(
                        message: '所有平台构建完成，是否继续部署？',
                        parameters: [
                            choice(
                                name: 'DEPLOY_ACTION',
                                choices: ['deploy', 'skip', 'manual'],
                                description: '选择部署动作'
                            )
                        ]
                    )
                    
                    if (shouldDeploy == 'deploy') {
                        echo "开始自动部署..."
                        // 执行部署逻辑
                    } else if (shouldDeploy == 'manual') {
                        echo "等待手动部署..."
                    } else {
                        echo "跳过部署"
                    }
                }
            }
        }
    }
    
    post {
        always {
            node('any') {
                echo "清理工作空间..."
                // cleanWs()
            }
        }
        
        success {
            node('any') {
                echo "🎉 所有平台构建成功！"
                script {
                    def buildMatrix = readJSON text: env.BUILD_MATRIX_JSON
                    echo "成功构建 ${buildMatrix.size()} 个平台配置"
                }
            }
        }
        
        unstable {
            node('any') {
                echo "⚠️ 部分构建不稳定"
                echo "请检查构建日志"
            }
        }
        
        failure {
            node('any') {
                echo "❌ 构建失败"
                echo "请检查失败的平台和错误信息"
            }
        }
    }
}

/*
这个Pipeline演示了：
1. 动态构建矩阵生成
2. 复杂的并行执行
3. 多平台构建支持
4. 嵌套并行测试
5. 构建结果汇总
6. 条件部署决策

学习要点：
- 使用script块进行复杂逻辑处理
- 动态生成并行任务
- 跨平台构建的处理方式
- 构建状态管理
- 人工干预和决策点

适用场景：
- 多平台应用构建
- 多版本兼容性测试
- 复杂的CI/CD流程
- 企业级构建系统
*/
