// Git集成的Jenkins Pipeline示例
// 演示如何从Git仓库检出代码并进行基本的CI流程

pipeline {
    agent any
    
    // 环境变量
    environment {
        // Git相关
        GIT_REPO = 'https://github.com/your-username/your-repo.git'
        GIT_BRANCH = 'main'
        
        // 构建相关
        BUILD_DIR = 'build'
        ARTIFACT_NAME = 'my-app'
    }
    
    // 构建参数
    parameters {
        string(
            name: 'BRANCH_NAME',
            defaultValue: 'main',
            description: 'Git分支名称'
        )
        
        booleanParam(
            name: 'CLEAN_WORKSPACE',
            defaultValue: false,
            description: '是否清理工作空间'
        )
    }
    
    stages {
        stage('准备工作空间') {
            steps {
                script {
                    if (params.CLEAN_WORKSPACE) {
                        echo "清理工作空间..."
                        deleteDir()
                    }
                }
                
                echo "工作空间准备完成"
                echo "当前目录: ${pwd()}"
            }
        }
        
        stage('检出代码') {
            steps {
                echo "从Git仓库检出代码..."
                echo "仓库: ${env.GIT_REPO}"
                echo "分支: ${params.BRANCH_NAME}"
                
                // 检出代码的几种方式
                
                // 方式1: 简单的git checkout
                // git url: env.GIT_REPO, branch: params.BRANCH_NAME
                
                // 方式2: 更详细的配置
                checkout([
                    $class: 'GitSCM',
                    branches: [[name: "*/${params.BRANCH_NAME}"]],
                    userRemoteConfigs: [[url: env.GIT_REPO]]
                ])
                
                // 显示Git信息
                script {
                    if (isUnix()) {
                        sh 'git log --oneline -5'
                        sh 'git status'
                    } else {
                        bat 'git log --oneline -5'
                        bat 'git status'
                    }
                }
            }
        }
        
        stage('代码分析') {
            parallel {
                stage('文件统计') {
                    steps {
                        echo "统计代码文件..."
                        script {
                            if (isUnix()) {
                                sh '''
                                    echo "=== 文件统计 ==="
                                    find . -name "*.js" -o -name "*.py" -o -name "*.java" | wc -l
                                    echo "=== 代码行数 ==="
                                    find . -name "*.js" -o -name "*.py" -o -name "*.java" | xargs wc -l
                                '''
                            } else {
                                bat '''
                                    echo "=== 文件统计 ==="
                                    dir /s /b *.js *.py *.java 2>nul | find /c /v ""
                                '''
                            }
                        }
                    }
                }
                
                stage('依赖检查') {
                    steps {
                        echo "检查项目依赖..."
                        script {
                            // 检查不同类型的依赖文件
                            if (fileExists('package.json')) {
                                echo "发现Node.js项目"
                                if (isUnix()) {
                                    sh 'cat package.json | grep -A 10 "dependencies"'
                                } else {
                                    bat 'type package.json | findstr "dependencies"'
                                }
                            }
                            
                            if (fileExists('requirements.txt')) {
                                echo "发现Python项目"
                                if (isUnix()) {
                                    sh 'cat requirements.txt'
                                } else {
                                    bat 'type requirements.txt'
                                }
                            }
                            
                            if (fileExists('pom.xml')) {
                                echo "发现Maven项目"
                                if (isUnix()) {
                                    sh 'cat pom.xml | grep -A 5 "<dependencies>"'
                                } else {
                                    bat 'type pom.xml | findstr "dependencies"'
                                }
                            }
                        }
                    }
                }
            }
        }
        
        stage('构建准备') {
            steps {
                echo "准备构建环境..."
                
                // 创建构建目录
                script {
                    if (isUnix()) {
                        sh "mkdir -p ${env.BUILD_DIR}"
                    } else {
                        bat "if not exist ${env.BUILD_DIR} mkdir ${env.BUILD_DIR}"
                    }
                }
                
                // 显示构建信息
                echo "构建目录: ${env.BUILD_DIR}"
                echo "构件名称: ${env.ARTIFACT_NAME}"
                echo "Git提交: ${env.GIT_COMMIT}"
                echo "Git分支: ${env.GIT_BRANCH}"
            }
        }
        
        stage('模拟构建') {
            steps {
                echo "开始构建..."
                
                script {
                    // 模拟不同类型项目的构建
                    if (fileExists('package.json')) {
                        echo "构建Node.js项目"
                        // sh 'npm install'
                        // sh 'npm run build'
                    } else if (fileExists('requirements.txt')) {
                        echo "构建Python项目"
                        // sh 'pip install -r requirements.txt'
                        // sh 'python setup.py build'
                    } else if (fileExists('pom.xml')) {
                        echo "构建Maven项目"
                        // sh 'mvn clean compile'
                    } else {
                        echo "通用构建流程"
                        if (isUnix()) {
                            sh 'echo "模拟编译过程..." > ${BUILD_DIR}/build.log'
                        } else {
                            bat 'echo "模拟编译过程..." > ${BUILD_DIR}\\build.log'
                        }
                    }
                }
                
                echo "构建完成"
            }
        }
        
        stage('归档构件') {
            steps {
                echo "归档构建产物..."
                
                // 归档文件
                script {
                    if (isUnix()) {
                        sh '''
                            echo "创建构建产物..."
                            echo "Build completed at $(date)" > ${BUILD_DIR}/artifact.txt
                            echo "Git commit: ${GIT_COMMIT}" >> ${BUILD_DIR}/artifact.txt
                            echo "Branch: ${GIT_BRANCH}" >> ${BUILD_DIR}/artifact.txt
                        '''
                    } else {
                        bat '''
                            echo "创建构建产物..."
                            echo Build completed at %date% %time% > ${BUILD_DIR}\\artifact.txt
                            echo Git commit: ${GIT_COMMIT} >> ${BUILD_DIR}\\artifact.txt
                            echo Branch: ${GIT_BRANCH} >> ${BUILD_DIR}\\artifact.txt
                        '''
                    }
                }
                
                // 归档构件（如果存在）
                archiveArtifacts artifacts: "${env.BUILD_DIR}/**", allowEmptyArchive: true
            }
        }
    }
    
    post {
        always {
            echo "清理临时文件..."
            // cleanWs() // 清理工作空间
        }
        
        success {
            echo "构建成功完成！"
            echo "Git提交: ${env.GIT_COMMIT}"
            echo "构建号: ${env.BUILD_NUMBER}"
        }
        
        failure {
            echo "构建失败！"
            echo "请检查构建日志"
        }
        
        unstable {
            echo "构建不稳定"
        }
    }
}

/*
使用说明：
1. 修改GIT_REPO变量为你的实际仓库地址
2. 确保Jenkins有访问Git仓库的权限
3. 根据你的项目类型调整构建命令

Git集成要点：
- checkout步骤用于检出代码
- 可以指定分支、标签或提交
- 环境变量GIT_COMMIT、GIT_BRANCH等自动可用
- fileExists()函数检查文件是否存在
- archiveArtifacts归档构建产物

学习要点：
- Git集成的基本用法
- 并行执行多个任务
- 条件判断和文件检查
- 构建产物归档
- 工作空间管理
*/
