// 带参数的Jenkins Pipeline示例
// 演示如何使用构建参数

pipeline {
    agent any
    
    // 定义构建参数
    parameters {
        // 字符串参数
        string(
            name: 'ENVIRONMENT',
            defaultValue: 'dev',
            description: '部署环境 (dev/test/prod)'
        )
        
        // 选择参数
        choice(
            name: 'BUILD_TYPE',
            choices: ['debug', 'release'],
            description: '构建类型'
        )
        
        // 布尔参数
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: false,
            description: '是否跳过测试'
        )
        
        // 文本参数
        text(
            name: 'DEPLOY_NOTES',
            defaultValue: '常规部署',
            description: '部署说明'
        )
    }
    
    // 环境变量
    environment {
        // 全局环境变量
        APP_NAME = 'my-application'
        VERSION = '1.0.0'
        
        // 基于参数的环境变量
        DEPLOY_ENV = "${params.ENVIRONMENT}"
    }
    
    stages {
        stage('参数验证') {
            steps {
                echo "=== 构建参数 ==="
                echo "环境: ${params.ENVIRONMENT}"
                echo "构建类型: ${params.BUILD_TYPE}"
                echo "跳过测试: ${params.SKIP_TESTS}"
                echo "部署说明: ${params.DEPLOY_NOTES}"
                
                echo "=== 环境变量 ==="
                echo "应用名称: ${env.APP_NAME}"
                echo "版本: ${env.VERSION}"
                echo "部署环境: ${env.DEPLOY_ENV}"
            }
        }
        
        stage('环境检查') {
            steps {
                script {
                    // 验证环境参数
                    if (!params.ENVIRONMENT.matches('dev|test|prod')) {
                        error("无效的环境参数: ${params.ENVIRONMENT}")
                    }
                    
                    echo "环境验证通过: ${params.ENVIRONMENT}"
                }
            }
        }
        
        stage('构建') {
            steps {
                echo "开始构建..."
                echo "构建类型: ${params.BUILD_TYPE}"
                
                script {
                    if (params.BUILD_TYPE == 'release') {
                        echo "执行发布版本构建"
                        // sh 'make release'
                    } else {
                        echo "执行调试版本构建"
                        // sh 'make debug'
                    }
                }
            }
        }
        
        stage('测试') {
            when {
                // 只有当SKIP_TESTS为false时才执行
                not { params.SKIP_TESTS }
            }
            steps {
                echo "执行测试..."
                echo "测试环境: ${params.ENVIRONMENT}"
                
                // 模拟测试命令
                script {
                    if (isUnix()) {
                        sh 'echo "运行单元测试"'
                        sh 'echo "运行集成测试"'
                    } else {
                        bat 'echo "运行单元测试"'
                        bat 'echo "运行集成测试"'
                    }
                }
            }
        }
        
        stage('部署') {
            when {
                // 只有在非dev环境才部署
                not { environment name: 'DEPLOY_ENV', value: 'dev' }
            }
            steps {
                echo "部署到环境: ${params.ENVIRONMENT}"
                echo "部署说明: ${params.DEPLOY_NOTES}"
                
                script {
                    switch(params.ENVIRONMENT) {
                        case 'test':
                            echo "部署到测试环境"
                            // sh 'deploy-to-test.sh'
                            break
                        case 'prod':
                            echo "部署到生产环境"
                            // sh 'deploy-to-prod.sh'
                            break
                        default:
                            echo "跳过部署"
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo "构建完成"
            echo "最终环境: ${params.ENVIRONMENT}"
        }
        
        success {
            script {
                if (params.ENVIRONMENT == 'prod') {
                    echo "生产环境部署成功！"
                    // 发送成功通知
                }
            }
        }
        
        failure {
            echo "构建失败，请检查日志"
            // 发送失败通知
        }
    }
}

/*
使用说明：
1. 这个Pipeline演示了如何使用各种类型的参数
2. 参数可以在Jenkins UI中手动输入
3. 也可以通过API调用时传递参数
4. when指令用于条件执行

参数类型：
- string: 字符串输入
- choice: 下拉选择
- booleanParam: 复选框
- text: 多行文本
- password: 密码输入

学习要点：
- parameters块定义构建参数
- 使用params.PARAMETER_NAME访问参数值
- environment块定义环境变量
- when指令实现条件执行
- script块允许使用Groovy脚本
*/
