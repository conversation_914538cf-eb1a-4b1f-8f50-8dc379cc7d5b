# Jenkins Pipeline 基础概念

## 1. 什么是Jenkins Pipeline？

Jenkins Pipeline是一套插件，支持在Jenkins中实现和集成持续交付管道。它提供了一套可扩展的工具，用于将简单到复杂的交付流程建模为"代码"。

### 主要优势：
- **代码化**：Pipeline以代码形式存在，可以版本控制
- **持久性**：Pipeline可以在Jenkins重启后继续执行
- **可暂停**：Pipeline可以暂停并等待人工输入或批准
- **多功能**：支持复杂的实际CD需求

## 2. Pipeline语法类型

### 声明式Pipeline (Declarative)
- 更简单、结构化的语法
- 更容易学习和使用
- 有更好的语法验证
- 推荐用于大多数用例

```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                echo 'Building...'
            }
        }
    }
}
```

### 脚本式Pipeline (Scripted)
- 基于Groovy的更灵活语法
- 更强大但更复杂
- 适用于复杂的自定义逻辑

```groovy
node {
    stage('Build') {
        echo 'Building...'
    }
}
```

## 3. 核心概念

### Pipeline
整个构建过程的定义，包含所有的工作流程。

### Node/Agent
执行Pipeline的机器或容器。

### Stage
Pipeline的逻辑分组，如"构建"、"测试"、"部署"。

### Step
单个任务，如执行shell命令、调用构建工具等。

## 4. Jenkinsfile

Jenkinsfile是包含Pipeline定义的文本文件，通常存储在项目的根目录中。

### 优势：
- 版本控制
- 代码审查
- 与项目代码一起演进
- 分支特定的Pipeline

## 5. 基本结构

```groovy
pipeline {
    agent any                    // 在任何可用的代理上执行
    
    environment {               // 环境变量
        MY_VAR = 'value'
    }
    
    parameters {                // 构建参数
        string(name: 'BRANCH', defaultValue: 'main')
    }
    
    stages {                    // 阶段定义
        stage('Checkout') {     // 检出代码
            steps {
                git 'https://github.com/user/repo.git'
            }
        }
        
        stage('Build') {        // 构建
            steps {
                sh 'make build'
            }
        }
        
        stage('Test') {         // 测试
            steps {
                sh 'make test'
            }
        }
        
        stage('Deploy') {       // 部署
            steps {
                sh 'make deploy'
            }
        }
    }
    
    post {                      // 后处理
        always {
            echo 'Pipeline completed'
        }
        success {
            echo 'Pipeline succeeded'
        }
        failure {
            echo 'Pipeline failed'
        }
    }
}
```

## 下一步

学习完基础概念后，建议：
1. 查看基础示例
2. 创建第一个简单的Pipeline
3. 逐步学习更多指令和功能
