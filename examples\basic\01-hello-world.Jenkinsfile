// 最简单的Jenkins Pipeline示例
// 文件名：Jenkinsfile

pipeline {
    // 指定在任何可用的代理上执行
    agent any
    
    // 定义构建阶段
    stages {
        // 第一个阶段：Hello World
        stage('Hello World') {
            steps {
                // 输出Hello World消息
                echo 'Hello, Jenkins Pipeline!'
                echo '这是我的第一个Pipeline'
            }
        }
        
        // 第二个阶段：显示环境信息
        stage('Environment Info') {
            steps {
                // 显示当前工作目录
                echo "当前工作目录: ${pwd()}"
                
                // 显示环境变量
                echo "构建号: ${env.BUILD_NUMBER}"
                echo "作业名称: ${env.JOB_NAME}"
                echo "Jenkins URL: ${env.JENKINS_URL}"
            }
        }
        
        // 第三个阶段：执行简单命令
        stage('Simple Commands') {
            steps {
                // 根据操作系统执行不同命令
                script {
                    if (isUnix()) {
                        sh 'echo "运行在Unix/Linux系统上"'
                        sh 'date'
                        sh 'whoami'
                    } else {
                        bat 'echo "运行在Windows系统上"'
                        bat 'date /t'
                        bat 'echo %USERNAME%'
                    }
                }
            }
        }
    }
    
    // 后处理阶段
    post {
        // 无论成功失败都会执行
        always {
            echo 'Pipeline执行完成！'
        }
        
        // 只有成功时执行
        success {
            echo 'Pipeline执行成功！'
        }
        
        // 只有失败时执行
        failure {
            echo 'Pipeline执行失败！'
        }
    }
}

/*
使用说明：
1. 将此文件重命名为 Jenkinsfile（无扩展名）
2. 放在你的Git仓库根目录
3. 在Jenkins中创建Pipeline项目
4. 配置项目从SCM获取Pipeline脚本
5. 运行构建

学习要点：
- pipeline块是声明式Pipeline的根元素
- agent指定执行环境
- stages包含所有构建阶段
- stage定义单个构建阶段
- steps包含具体的执行步骤
- post定义后处理逻辑
*/
