// 简单Web应用的完整CI/CD Pipeline
// 这是一个实际的练习项目

pipeline {
    agent any
    
    environment {
        // 应用配置
        APP_NAME = 'simple-web-app'
        APP_VERSION = '1.0.0'
        
        // Docker配置
        DOCKER_IMAGE = "${APP_NAME}:${BUILD_NUMBER}"
        DOCKER_REGISTRY = 'your-registry.com'
        
        // 部署配置
        DEV_SERVER = 'dev.example.com'
        PROD_SERVER = 'prod.example.com'
    }
    
    parameters {
        choice(
            name: 'DEPLOY_ENV',
            choices: ['none', 'dev', 'staging', 'prod'],
            description: '选择部署环境'
        )
        
        booleanParam(
            name: 'RUN_TESTS',
            defaultValue: true,
            description: '是否运行测试'
        )
        
        booleanParam(
            name: 'BUILD_DOCKER',
            defaultValue: false,
            description: '是否构建Docker镜像'
        )
    }
    
    stages {
        stage('代码检出') {
            steps {
                echo "检出代码..."
                // git 'https://github.com/your-username/simple-web-app.git'
                
                // 创建示例项目结构
                script {
                    if (isUnix()) {
                        sh '''
                            mkdir -p src tests docs
                            echo "console.log('Hello World');" > src/app.js
                            echo "<!DOCTYPE html><html><head><title>Simple App</title></head><body><h1>Hello World</h1></body></html>" > src/index.html
                            echo "describe('App', () => { it('should work', () => { expect(true).toBe(true); }); });" > tests/app.test.js
                            echo "# Simple Web App" > docs/README.md
                        '''
                    } else {
                        bat '''
                            if not exist src mkdir src
                            if not exist tests mkdir tests
                            if not exist docs mkdir docs
                            echo console.log('Hello World'); > src\\app.js
                            echo ^<!DOCTYPE html^>^<html^>^<head^>^<title^>Simple App^</title^>^</head^>^<body^>^<h1^>Hello World^</h1^>^</body^>^</html^> > src\\index.html
                            echo describe('App', () =^> { it('should work', () =^> { expect(true).toBe(true); }); }); > tests\\app.test.js
                            echo # Simple Web App > docs\\README.md
                        '''
                    }
                }
            }
        }
        
        stage('环境准备') {
            parallel {
                stage('安装依赖') {
                    steps {
                        echo "安装项目依赖..."
                        script {
                            // 创建package.json
                            writeFile file: 'package.json', text: '''
{
  "name": "simple-web-app",
  "version": "1.0.0",
  "description": "A simple web application",
  "main": "src/app.js",
  "scripts": {
    "start": "node src/app.js",
    "test": "jest",
    "build": "echo 'Building application...'",
    "lint": "eslint src/"
  },
  "devDependencies": {
    "jest": "^27.0.0",
    "eslint": "^8.0.0"
  }
}
'''
                            
                            // 模拟npm install
                            echo "npm install (模拟)"
                            // sh 'npm install'
                        }
                    }
                }
                
                stage('代码质量检查') {
                    steps {
                        echo "运行代码质量检查..."
                        
                        script {
                            if (isUnix()) {
                                sh '''
                                    echo "运行ESLint..."
                                    echo "✓ src/app.js - 0 errors, 0 warnings"
                                    echo "✓ 代码质量检查通过"
                                '''
                            } else {
                                bat '''
                                    echo "运行ESLint..."
                                    echo "✓ src/app.js - 0 errors, 0 warnings"
                                    echo "✓ 代码质量检查通过"
                                '''
                            }
                        }
                    }
                }
            }
        }
        
        stage('运行测试') {
            when {
                equals expected: true, actual: params.RUN_TESTS
            }
            steps {
                echo "运行单元测试..."
                
                script {
                    if (isUnix()) {
                        sh '''
                            echo "运行Jest测试..."
                            echo "PASS tests/app.test.js"
                            echo "Test Suites: 1 passed, 1 total"
                            echo "Tests: 1 passed, 1 total"
                            echo "✓ 所有测试通过"
                        '''
                    } else {
                        bat '''
                            echo "运行Jest测试..."
                            echo "PASS tests/app.test.js"
                            echo "Test Suites: 1 passed, 1 total"
                            echo "Tests: 1 passed, 1 total"
                            echo "✓ 所有测试通过"
                        '''
                    }
                }
                
                // 发布测试结果（如果有实际的测试报告）
                // publishTestResults testResultsPattern: 'test-results.xml'
            }
        }
        
        stage('构建应用') {
            steps {
                echo "构建应用..."
                
                script {
                    if (isUnix()) {
                        sh '''
                            echo "创建构建目录..."
                            mkdir -p dist
                            
                            echo "复制源文件..."
                            cp -r src/* dist/
                            
                            echo "生成版本信息..."
                            echo "{\\"version\\": \\"${APP_VERSION}\\", \\"build\\": \\"${BUILD_NUMBER}\\", \\"timestamp\\": \\"$(date)\\"}" > dist/version.json
                            
                            echo "构建完成"
                        '''
                    } else {
                        bat '''
                            echo "创建构建目录..."
                            if not exist dist mkdir dist
                            
                            echo "复制源文件..."
                            xcopy src\\* dist\\ /E /Y
                            
                            echo "生成版本信息..."
                            echo {"version": "${APP_VERSION}", "build": "${BUILD_NUMBER}", "timestamp": "%date% %time%"} > dist\\version.json
                            
                            echo "构建完成"
                        '''
                    }
                }
                
                // 归档构建产物
                archiveArtifacts artifacts: 'dist/**', fingerprint: true
            }
        }
        
        stage('Docker构建') {
            when {
                equals expected: true, actual: params.BUILD_DOCKER
            }
            steps {
                echo "构建Docker镜像..."
                
                script {
                    // 创建Dockerfile
                    writeFile file: 'Dockerfile', text: '''
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
'''
                    
                    echo "Docker镜像: ${env.DOCKER_IMAGE}"
                    // docker.build(env.DOCKER_IMAGE)
                    echo "Docker镜像构建完成"
                }
            }
        }
        
        stage('部署') {
            when {
                not { equals expected: 'none', actual: params.DEPLOY_ENV }
            }
            steps {
                echo "部署到环境: ${params.DEPLOY_ENV}"
                
                script {
                    switch(params.DEPLOY_ENV) {
                        case 'dev':
                            echo "部署到开发环境: ${env.DEV_SERVER}"
                            // sh "scp -r dist/* user@${env.DEV_SERVER}:/var/www/html/"
                            break
                            
                        case 'staging':
                            echo "部署到预发布环境"
                            // 需要人工确认
                            input message: '确认部署到预发布环境?', ok: '部署'
                            echo "部署到预发布环境完成"
                            break
                            
                        case 'prod':
                            echo "部署到生产环境: ${env.PROD_SERVER}"
                            // 需要人工确认
                            input message: '确认部署到生产环境?', ok: '部署', submitterParameter: 'DEPLOYER'
                            echo "部署到生产环境完成"
                            echo "部署人员: ${env.DEPLOYER}"
                            break
                            
                        default:
                            echo "未知的部署环境: ${params.DEPLOY_ENV}"
                    }
                }
            }
        }
        
        stage('健康检查') {
            when {
                anyOf {
                    equals expected: 'staging', actual: params.DEPLOY_ENV
                    equals expected: 'prod', actual: params.DEPLOY_ENV
                }
            }
            steps {
                echo "执行健康检查..."
                
                script {
                    if (isUnix()) {
                        sh '''
                            echo "检查应用状态..."
                            echo "✓ 应用启动正常"
                            echo "✓ 健康检查通过"
                        '''
                    } else {
                        bat '''
                            echo "检查应用状态..."
                            echo "✓ 应用启动正常"
                            echo "✓ 健康检查通过"
                        '''
                    }
                }
            }
        }
    }
    
    post {
        always {
            echo "Pipeline执行完成"
            echo "构建号: ${env.BUILD_NUMBER}"
            echo "应用版本: ${env.APP_VERSION}"
        }
        
        success {
            echo "🎉 Pipeline执行成功！"
            script {
                if (params.DEPLOY_ENV != 'none') {
                    echo "✅ 应用已成功部署到: ${params.DEPLOY_ENV}"
                }
            }
        }
        
        failure {
            echo "❌ Pipeline执行失败"
            echo "请检查构建日志并修复问题"
        }
        
        unstable {
            echo "⚠️ Pipeline执行不稳定"
            echo "可能存在测试失败或其他问题"
        }
        
        cleanup {
            echo "清理工作空间..."
            // cleanWs()
        }
    }
}

/*
这个Pipeline演示了：
1. 完整的CI/CD流程
2. 并行执行
3. 条件部署
4. 人工确认
5. 健康检查
6. Docker集成
7. 构件归档

练习建议：
1. 复制这个文件到你的项目
2. 根据实际项目调整配置
3. 逐步添加真实的构建命令
4. 集成实际的测试框架
5. 配置真实的部署脚本
*/
